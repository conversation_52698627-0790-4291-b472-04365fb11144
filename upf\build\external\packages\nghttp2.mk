# Copyright (c) 2018 Cisco and/or its affiliates.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at:
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

nghttp2_version             := 1.40.0
nghttp2_tarball             := nghttp2-$(nghttp2_version).tar.gz
nghttp2_tarball_md5sum      := 5df375bbd532fcaa7cd4044b54b1188d
nghttp2_tarball_strip_dirs  := 1
nghttp2_url                 := https://github.com/nghttp2/nghttp2/releases/download/v1.40.0/$(nghttp2_tarball)

define  nghttp2_config_cmds
	@true
endef

define  nghttp2_build_cmds
	echo "BUILDING nghttp2"
	@cd $(nghttp2_src_dir) && \
		echo $(nghttp2_install_dir) && \
		./configure --enable-lib-only --enable-shared --prefix=$(nghttp2_install_dir) && \
		$(MAKE) $(MAKE_ARGS) VARIANT=release > $(nghttp2_build_log)
endef

define  nghttp2_install_cmds
	@make -C $(nghttp2_src_dir) \
	  VARIANT=release \
	  PREFIX=$(nghttp2_install_dir) \
	  install > $(nghttp2_install_log)
endef

$(eval $(call package,nghttp2))


