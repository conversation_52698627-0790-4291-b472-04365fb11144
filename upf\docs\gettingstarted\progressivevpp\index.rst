.. _progressivevpp:

.. toctree::

########################
Progressive VPP Tutorial
########################

Learn to run FD.io VPP on a single Ubuntu 16.04 VM using Vagrant with this walkthrough
covering basic FD.io VPP scenarios. Useful FD.io VPP commands will be used, and
will discuss basic operations, and the state of a running FD.io VPP on a system.

.. note::

    This is **not** intended to be a 'How to Run in a Production Environment' set of instructions.

For more information on using VPP with Virtual Box/Vagrant, please refer to :ref:`vppvagrant`

.. toctree::

   settingupenvironment.rst
   runningvpp.rst
   interface.rst
   traces.rst
   twovppinstances.rst
   routing.rst
   switching.rst
