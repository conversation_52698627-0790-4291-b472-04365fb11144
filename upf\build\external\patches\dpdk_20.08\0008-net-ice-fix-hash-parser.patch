From e9662a9bcf446a99eab9daee31d0a0a69f29950a Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 31 Aug 2020 13:50:45 +0800
Subject: [PATCH 08/72] net/ice: fix hash parser

GTPU TEID hash should only be enabled when ETH_RSS_GTPU is required.
And the hash parser should not restrict the combined usage of protocol.

Fixes: e7cc68c70736 ("net/ice: fix GTPU TEID hash")
Cc: <EMAIL>

Signed-off-by: <PERSON> <<EMAIL>>
Acked-by: <PERSON> <<EMAIL>>
---
 drivers/net/ice/ice_hash.c | 7 +++++--
 1 file changed, 5 insertions(+), 2 deletions(-)

diff --git a/drivers/net/ice/ice_hash.c b/drivers/net/ice/ice_hash.c
index c0271dff5..45c69e6bf 100644
--- a/drivers/net/ice/ice_hash.c
+++ b/drivers/net/ice/ice_hash.c
@@ -1141,7 +1141,7 @@ ice_hash_parse_action(struct ice_pattern_match_item *pattern_match_item,
 			}
 
 			/* update hash field for nat-t esp. */
-			if (rss_type == ETH_RSS_ESP &&
+			if (rss_type & ETH_RSS_ESP &&
 			    (m->eth_rss_hint & ETH_RSS_NONFRAG_IPV4_UDP ||
 			     m->eth_rss_hint & ETH_RSS_NONFRAG_IPV6_UDP)) {
 				hash_meta->hash_flds &=
@@ -1151,7 +1151,10 @@ ice_hash_parse_action(struct ice_pattern_match_item *pattern_match_item,
 			}
 
 			/* update hash field for gtpu eh/gtpu dwn/gtpu up. */
-			if (hash_meta->pkt_hdr & ICE_FLOW_SEG_HDR_GTPU_EH) {
+			if (!(rss_type & ETH_RSS_GTPU)) {
+				break;
+			} else if (hash_meta->pkt_hdr &
+				   ICE_FLOW_SEG_HDR_GTPU_EH) {
 				hash_meta->hash_flds &=
 				~(BIT_ULL(ICE_FLOW_FIELD_IDX_GTPU_IP_TEID));
 				hash_meta->hash_flds |=
-- 
2.17.1

