# Copyright (c) 2018 Cisco and/or its affiliates.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at:
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

libcyaml_version             := 0.1.0
libcyaml_tarball             := v$(libcyaml_version).tar.gz
libcyaml_tarball_md5sum      := f14c3280d76dedfc1821c9cb20d878e3
libcyaml_tarball_strip_dirs  := 1
libcyaml_depends             := nasm
libcyaml_url                 := https://github.com/tlsa/libcyaml/archive/$(libcyaml_tarball)

define  libcyaml_config_cmds
	@true
endef

define  libcyaml_build_cmds
	echo "BUILDING"
	make -C $(libcyaml_src_dir) VARIANT=release 
endef

define  libcyaml_install_cmds
	@make -C $(libcyaml_src_dir) \
	  VARIANT=release \
	  PREFIX=$(libcyaml_install_dir) \
	  install > $(libcyaml_install_log)
endef

$(eval $(call package,libcyaml))


