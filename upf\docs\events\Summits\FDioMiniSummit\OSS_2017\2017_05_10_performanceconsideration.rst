.. _2017_05_10_performanceconsideration:

.. toctree::

######################################################################
Performance Considerations for Packet Processing on Intel Architecture
######################################################################

Event
-----

FD.io VPP is a modular, fast, scalable and deterministic data plane for NFV network workloads. Some common uses cases for VPP are as a vSwitch, a L3 Router, a vTEP, a Load Balancer, sockets applications and so much more, out of the box - no hacking required! It is optimized to take advantage of Intel Architecture Core and Platform accelerations to accelerate dataplane packet processing. It is permissively licensed as Apache 2, with a welcoming community of active developers behind it. It is extensible and can be used as an excellent set of building blocks to accelerate your next development project.

This presentation covers the following content:
* Introduction to the architecture and design of VPP.
* A walk through common VPP use cases, cloud and NFV integration.
* Creating Network Nodes: accelerating VPP IPSEC with Intel Technologies.
* Performance analysis on Intel Architecture.

This presentation was held during the on May 10th, 2017.

Speakers
--------

* Patrick Lu
* Sergio Gonzalez Monroy

Slideshow
---------

`Presentation Powerpoint <https://wiki.fd.io/images/7/7b/Performance_Consideration_for_packet_processing_on_Intel_Architecture.pptx>`_

Video
-----

`Video Presentation <https://www.youtube.com/watch?v=Pt7E1lXZO90>`_
