## Features for Release VPP 19.01

### Infrastructure
- NUMA-aware, growable physical memory allocator (pmalloc)
- FIB: sticky load-balance
- C11 safe string handling: provide and use "safe" C string handling functions
- vlib: allocate buffers on local numa, not on numa 1
- vppinfra: autodetect default hugepage size
- Move RPC traffic off the shared-memory API queue
- IPv6: Make link-local configurable per-interface
- IGMP: improve CLI debug output
- IPSec: split ipsec nodes into ip4/ip6 nodes
- IPSec: infra for selecting backends
- vhost-user: cleanup and performance optimizations
- ethernet-input, memif improvements and optimizations
- DPDK: bump to DPDK 18.11
- reassembly: harden reassembly code
- stats: Deprecate old (event-based) stats framework
- vlib: support Hyper-V/Azure VMBus
- binary api clients: wait for vpp to start
- graph dispatch trace: capture packet data and buffer metadata, output in pcap format
- improve feature arc order constraint specification

### VNET & Plugins
- pktgen: correctly replay a mix of single and multi-buffer packets
- add wireshark dissector to extras
- avf: optimizations
- acl-plugin: use L2 feature arc instead of L2 classifier
- acl-plugin: performance enhancement
- dpdk: allow interface name to be specified from startup.conf
- dpdk: blacklist PCI devices by type
- dpdk: switch to in-memory mode, deprecate use of socket-mem
- vnet: store hw interface speed in kbps instead of using flags
- vmxnet3: enable promiscuous mode & cli enhancements
- gbp: Add support for flow hash profile & l3-out subnets
- map: Add API support for setting parameters.
- map: Convert from DPO to input feature
- nat: improve expired sessions reuse in NAT44
- nat: syslog - sessions logging
- nsim: add packet loss simulation, docs
- perfmon: x86_64 perf counter plugin
- vnet: L2 feature arc infrastructure

### Host stack
- TCP congestion control improvements
- TCP Cubic congestion control algorithm
- TCP fast path optimizations
- Transport tx connection pacer. TCP uses it by default
- Basic support for session flushing and TCP PSH segments
- TCP/session api support for configuring custom local src ip/port
- VCL/LDP basic support for multi-process applications
- Overall code hardening, cleanup and bugfixing for tcp, session, vcl and ldp

### PAPI & Test framework
- add specific API types for IP addresses, MAC address, interface index etc.
- add timeout support for socket transport
- add support for format/unformat functions
- generic API types format/unformat support for VAT and custom dump
- python3 test adjustments
- make test: create virtualenv under /test/
- make test: print TEST= values for failed tests
- add human-friendly annotations to log messages

### VOM
- Add support for redirect contracts in gbp
- deprecate TAP add ip-punt redirect dump
- vxlan-gbp support
