Format: 1.0
Source: vpp
Binary: vpp vpp-dbg vpp-dev libvppinfra libvppinfra-dev vpp-plugin-core vpp-plugin-dpdk vpp-api-python python3-vpp-api
Architecture: amd64
Version: 24.06-rc1.4~14-g206fe9ca1
Checksums-Md5:
 886496f27185b76692307add6c29df90 129868 libvppinfra-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 1ef70c32b605be95315424f4a1245b29 149952 libvppinfra_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 1539168e5e06d3bfcf86b61ea0104efd 24884 python3-vpp-api_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 39445f522faeea875750f1eef66a6cbb 24820 vpp-api-python_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 2c380afcab9c04c6e581b911380f1236 57045116 vpp-dbg_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 4b7a4d588e26fcdfce72b48545ffe636 1069184 vpp-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 b9482bf36404cf2b58fe9e2315a6e9ab 4641880 vpp-plugin-core_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 68dfa4ca55216ccc12ef13c00b8f737a 4751644 vpp-plugin-dpdk_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 bad5d4f6a833e2e2230daa41a23b859a 5012840 vpp_24.06-rc1.4~14-g206fe9ca1_amd64.deb
Checksums-Sha1:
 c2a1ef9e9d52b5741db1d967e79c1809400e821c 129868 libvppinfra-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 c17310acae7b61bbd2ae2994f3444f45e84f29d1 149952 libvppinfra_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 02d295ff33faa4e44a8729d85a68cf2af4a06717 24884 python3-vpp-api_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 b652665062e5db12584240c76dd56109a7256603 24820 vpp-api-python_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 3790ee9800a78678d602df76bc268e1cf8576c45 57045116 vpp-dbg_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 8566cb27d78bad0515dc5a8e3bf1e59b0af5c54f 1069184 vpp-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 a76600332aa4fefc3fd33d7030af5ea574901a5d 4641880 vpp-plugin-core_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 ab15e75c2b690feb8ea4277dec13ec8304c3157c 4751644 vpp-plugin-dpdk_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 c71aab8545b3ba6c77ac02319499a4f9250ec948 5012840 vpp_24.06-rc1.4~14-g206fe9ca1_amd64.deb
Checksums-Sha256:
 dd2142326675b0efed01c7fba0e376e93f237c5e8acc2f94d4f0af7fac337b5f 129868 libvppinfra-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 199aec847617c0d549c99c369836258f4564ea9cdb50de9a8daaf1a048381bea 149952 libvppinfra_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 d0e069c60d657f56ce5d8856ed9b6a61fd98d6c3acc198fb04f64fd9902a58ef 24884 python3-vpp-api_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 411220d33c51dba09d72f74108440163d2bc68238fec82b611ff4feefcefadff 24820 vpp-api-python_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 a2dd27a2418e5b806f055a40a76f1ea2c3fcc80f92d31c8513cb38e0d455fa24 57045116 vpp-dbg_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 bcdc209acfb8b0fbd4d463684b75c87c97f6fb4a36f331ca988eb33fbfa1c5ae 1069184 vpp-dev_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 d8d7645844d8c2fbf3254c961d7d443f7693f6965af68bde6cdede949983d299 4641880 vpp-plugin-core_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 4d91ba37b171f297d28b9c9528eb4228aeca2b4ed3fc8dd03cefdf49283f24ae 4751644 vpp-plugin-dpdk_24.06-rc1.4~14-g206fe9ca1_amd64.deb
 8f79788ee2bfa332ab175bc5417afd73fa0aa70c003910ec080bc7653d0f3933 5012840 vpp_24.06-rc1.4~14-g206fe9ca1_amd64.deb
Build-Origin: Ubuntu
Build-Architecture: amd64
Build-Date: Tue, 04 Feb 2025 18:59:37 -0800
Installed-Build-Depends:
 autoconf (= 2.69-11),
 automake (= 1:1.15.1-3ubuntu2),
 autopoint (= ********-6ubuntu0.3),
 autotools-dev (= 20180224.1),
 base-files (= 10.1ubuntu2.8),
 base-passwd (= 3.5.44),
 bash (= 4.4.18-2ubuntu1.3),
 binutils (= 2.30-21ubuntu1~18.04.9),
 binutils-common (= 2.30-21ubuntu1~18.04.9),
 binutils-x86-64-linux-gnu (= 2.30-21ubuntu1~18.04.9),
 bsdmainutils (= 11.1.2ubuntu1),
 bsdutils (= 1:2.31.1-0.4ubuntu3.7),
 build-essential (= 12.4ubuntu1),
 bzip2 (= 1.0.6-8.1ubuntu0.2),
 clang-9 (= 1:9-2~ubuntu18.04.2),
 coreutils (= 8.28-1ubuntu1),
 cpp (= 4:7.4.0-1ubuntu2.3),
 cpp-7 (= 7.5.0-3ubuntu1~18.04),
 cpp-8 (= 8.4.0-1ubuntu1~18.04),
 dash (= 0.5.8-2.10),
 debconf (= 1.5.66ubuntu1),
 debhelper (= 11.1.6ubuntu2),
 debianutils (= 4.8.4),
 dh-autoreconf (= 17),
 dh-python (= 3.20180325ubuntu2),
 dh-strip-nondeterminism (= 0.040-1.1~build1),
 dh-systemd (= 11.1.6ubuntu2),
 diffutils (= 1:3.6-1),
 dpkg (= 1.19.0.5ubuntu2.4),
 dpkg-dev (= 1.19.0.5ubuntu2.4),
 e2fsprogs (= 1.44.1-1ubuntu1.4),
 fdisk (= 2.31.1-0.4ubuntu3.7),
 file (= 1:5.32-2ubuntu0.4),
 findutils (= 4.6.0+git+20170828-2),
 g++ (= 4:7.4.0-1ubuntu2.3),
 g++-7 (= 7.5.0-3ubuntu1~18.04),
 gcc (= 4:7.4.0-1ubuntu2.3),
 gcc-7 (= 7.5.0-3ubuntu1~18.04),
 gcc-7-base (= 7.5.0-3ubuntu1~18.04),
 gcc-8 (= 8.4.0-1ubuntu1~18.04),
 gcc-8-base (= 8.4.0-1ubuntu1~18.04),
 gettext (= ********-6ubuntu0.3),
 gettext-base (= ********-6ubuntu0.3),
 grep (= 3.1-2build1),
 groff-base (= 1.22.3-10),
 gzip (= 1.6-5ubuntu1.2),
 hostname (= 3.20),
 init-system-helpers (= 1.51),
 install-info (= 6.5.0.dfsg.1-2),
 intltool-debian (= 0.35.0+20060710.4),
 lib32gcc1 (= 1:8.4.0-1ubuntu1~18.04),
 lib32stdc++6 (= 8.4.0-1ubuntu1~18.04),
 libacl1 (= 2.2.52-3build1),
 libarchive-zip-perl (= 1.60-1ubuntu0.1),
 libasan4 (= 7.5.0-3ubuntu1~18.04),
 libasan5 (= 8.4.0-1ubuntu1~18.04),
 libatomic1 (= 8.4.0-1ubuntu1~18.04),
 libattr1 (= 1:2.4.47-2build1),
 libaudit-common (= 1:2.8.2-1ubuntu1),
 libaudit1 (= 1:2.8.2-1ubuntu1),
 libbinutils (= 2.30-21ubuntu1~18.04.9),
 libblkid1 (= 2.31.1-0.4ubuntu3.7),
 libbsd0 (= 0.8.7-1ubuntu0.1),
 libbz2-1.0 (= 1.0.6-8.1ubuntu0.2),
 libc-bin (= 2.27-3ubuntu1.5),
 libc-dev-bin (= 2.27-3ubuntu1.6),
 libc6 (= 2.27-3ubuntu1.6),
 libc6-dev (= 2.27-3ubuntu1.6),
 libc6-i386 (= 2.27-3ubuntu1.6),
 libcap-ng0 (= 0.7.7-3.1),
 libcc1-0 (= 8.4.0-1ubuntu1~18.04),
 libcilkrts5 (= 7.5.0-3ubuntu1~18.04),
 libclang-common-9-dev (= 1:9-2~ubuntu18.04.2),
 libclang-cpp9 (= 1:9-2~ubuntu18.04.2),
 libclang1-9 (= 1:9-2~ubuntu18.04.2),
 libcom-err2 (= 1.44.1-1ubuntu1.4),
 libcroco3 (= 0.6.12-2),
 libdb5.3 (= 5.3.28-13.1ubuntu1.1),
 libdebconfclient0 (= 0.213ubuntu1),
 libdpkg-perl (= 1.19.0.5ubuntu2.4),
 libedit2 (= 3.1-20170329-1),
 libexpat1 (= 2.2.5-3ubuntu0.9),
 libext2fs2 (= 1.44.1-1ubuntu1.4),
 libfdisk1 (= 2.31.1-0.4ubuntu3.7),
 libffi6 (= 3.2.1-8),
 libfile-stripnondeterminism-perl (= 0.040-1.1~build1),
 libgc1c2 (= 1:7.4.2-8ubuntu1),
 libgcc-7-dev (= 7.5.0-3ubuntu1~18.04),
 libgcc-8-dev (= 8.4.0-1ubuntu1~18.04),
 libgcc1 (= 1:8.4.0-1ubuntu1~18.04),
 libgcrypt20 (= 1.8.1-4ubuntu1.3),
 libgdbm-compat4 (= 1.14.1-6),
 libgdbm5 (= 1.14.1-6),
 libglib2.0-0 (= 2.56.4-0ubuntu0.18.04.9),
 libgmp10 (= 2:6.1.2+dfsg-2ubuntu0.1),
 libgomp1 (= 8.4.0-1ubuntu1~18.04),
 libgpg-error0 (= 1.27-6),
 libicu60 (= 60.2-3ubuntu3.2),
 libisl19 (= 0.19-1),
 libitm1 (= 8.4.0-1ubuntu1~18.04),
 libllvm9 (= 1:9-2~ubuntu18.04.2),
 liblsan0 (= 8.4.0-1ubuntu1~18.04),
 liblz4-1 (= 0.0~r131-2ubuntu3.1),
 liblzma5 (= 5.2.2-1.3ubuntu0.1),
 libmagic-mgc (= 1:5.32-2ubuntu0.4),
 libmagic1 (= 1:5.32-2ubuntu0.4),
 libmount1 (= 2.31.1-0.4ubuntu3.7),
 libmpc3 (= 1.1.0-1),
 libmpdec2 (= 2.4.2-1ubuntu1),
 libmpfr6 (= 4.0.1-1),
 libmpx2 (= 8.4.0-1ubuntu1~18.04),
 libncursesw5 (= 6.1-1ubuntu1.18.04.1),
 libobjc-7-dev (= 7.5.0-3ubuntu1~18.04),
 libobjc4 (= 8.4.0-1ubuntu1~18.04),
 libpam-modules (= 1.1.8-3.6ubuntu2.18.04.6),
 libpam-modules-bin (= 1.1.8-3.6ubuntu2.18.04.6),
 libpam-runtime (= 1.1.8-3.6ubuntu2.18.04.6),
 libpam0g (= 1.1.8-3.6ubuntu2.18.04.6),
 libpcre3 (= 2:8.39-9ubuntu0.1),
 libperl5.26 (= 5.26.1-6ubuntu0.7),
 libpipeline1 (= 1.5.0-1),
 libpython-stdlib (= 2.7.15~rc1-1),
 libpython2.7-minimal (= 2.7.17-1~18.04ubuntu1.11),
 libpython2.7-stdlib (= 2.7.17-1~18.04ubuntu1.11),
 libpython3-stdlib (= 3.6.7-1~18.04),
 libpython3.6-minimal (= 3.6.9-1~18.04ubuntu1.12),
 libpython3.6-stdlib (= 3.6.9-1~18.04ubuntu1.12),
 libquadmath0 (= 8.4.0-1ubuntu1~18.04),
 libreadline7 (= 7.0-3),
 libseccomp2 (= 2.5.1-1ubuntu1~18.04.2),
 libselinux1 (= 2.7-2build2),
 libsigsegv2 (= 2.12-1),
 libsmartcols1 (= 2.31.1-0.4ubuntu3.7),
 libsqlite3-0 (= 3.22.0-1ubuntu0.7),
 libss2 (= 1.44.1-1ubuntu1.4),
 libssl1.1 (= 1.1.1-1ubuntu2.1~18.04.23),
 libstdc++-7-dev (= 7.5.0-3ubuntu1~18.04),
 libstdc++6 (= 8.4.0-1ubuntu1~18.04),
 libsystemd0 (= 237-3ubuntu10.57),
 libtimedate-perl (= 2.3000-2),
 libtinfo5 (= 6.1-1ubuntu1.18.04.1),
 libtool (= 2.4.6-2),
 libtsan0 (= 8.4.0-1ubuntu1~18.04),
 libubsan0 (= 7.5.0-3ubuntu1~18.04),
 libubsan1 (= 8.4.0-1ubuntu1~18.04),
 libudev1 (= 237-3ubuntu10.57),
 libunistring2 (= 0.9.9-0ubuntu2),
 libuuid1 (= 2.31.1-0.4ubuntu3.7),
 libxml2 (= 2.9.4+dfsg1-6.1ubuntu1.9),
 libzstd1 (= 1.3.3+dfsg-2ubuntu1.2),
 linux-libc-dev (= 4.15.0-213.224),
 login (= 1:4.5-1ubuntu2.5),
 m4 (= 1.4.18-1),
 make (= 4.1-9.1ubuntu1),
 man-db (= 2.8.3-2ubuntu0.1),
 mawk (= 1.3.3-17ubuntu3),
 mime-support (= 3.60ubuntu1),
 ncurses-base (= 6.1-1ubuntu1.18.04.1),
 ncurses-bin (= 6.1-1ubuntu1.18.04.1),
 patch (= 2.7.6-2ubuntu1.1),
 perl (= 5.26.1-6ubuntu0.7),
 perl-base (= 5.26.1-6ubuntu0.7),
 perl-modules-5.26 (= 5.26.1-6ubuntu0.7),
 po-debconf (= 1.0.20),
 python (= 2.7.15~rc1-1),
 python-all (= 2.7.15~rc1-1),
 python-minimal (= 2.7.15~rc1-1),
 python2.7 (= 2.7.17-1~18.04ubuntu1.11),
 python2.7-minimal (= 2.7.17-1~18.04ubuntu1.11),
 python3 (= 3.6.7-1~18.04),
 python3-all (= 3.6.7-1~18.04),
 python3-distutils (= 3.6.9-1~18.04),
 python3-lib2to3 (= 3.6.9-1~18.04),
 python3-minimal (= 3.6.7-1~18.04),
 python3-pkg-resources (= 39.0.1-2ubuntu0.1),
 python3-setuptools (= 39.0.1-2ubuntu0.1),
 python3.6 (= 3.6.9-1~18.04ubuntu1.12),
 python3.6-minimal (= 3.6.9-1~18.04ubuntu1.12),
 readline-common (= 7.0-3),
 sed (= 4.4-2),
 sysvinit-utils (= 2.88dsf-59.10ubuntu1),
 tar (= 1.29b-2ubuntu0.4),
 util-linux (= 2.31.1-0.4ubuntu3.7),
 xz-utils (= 5.2.2-1.3ubuntu0.1),
 zlib1g (= 1:1.2.11.dfsg-0ubuntu2.2)
Environment:
 DEB_BUILD_OPTIONS="parallel=4"
 LANG="en_US.UTF-8"
 MAKEFLAGS="w -- TAG=vpp PLATFORM=vpp"
 SOURCE_DATE_EPOCH="1738723297"
