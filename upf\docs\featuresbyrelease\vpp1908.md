## Features for Release VPP 19.08

### Infrastructure
- API
  - API language: new types and limits support
  - Python API - add support for defaults
  - Export ip_types.api for out-of-tree plugins use
  - Refactor ipip.api with explicit types
- DPDK
  - 19.05 integration
  - Remove bonding code
  - Rework extended stats
- Debugging & Serviceability
  - debug CLI leak-checker
  - vlib: add "memory-trace stats-segment"
  - vppapitrace JSON/API trace converter
  - ARP: add arp-disabled node
  - igmp: Trace more data from input packets
  - ip: Trace the packet from the punt node
  - Python API debug introspection improvements
  - Pin dependencies for make test infra
  - FEATURE.yaml meta-data infrastructure
  - tcp: add cc stats plotting tools
  - Packet tracer support for thread handoffs
- libmemif: support for multi-thread connection establishment
- svm
  - fifo ooo reads/writes with multiple chunks
  - support addition/removal of chunks to fifos
- vppinfra
  - Mapped pcap file support
  - More AVX2 and AVX512 inlines
  - VLIB_INIT_FUNCTION sequencing rework
  - refactor spinlocks and rwlocks
  - add rbtree
  - add doubly linked list
- rdma: bump rdma-core to v25.0
- stats
  - Add the number of worker threads and per worker thread vector rates
  - Support multiple workers for error counters

### VNET & Plugins
- New Plugins
  - HTTP static page server with TLS support
  - L3 cross connect
- acl: implement stat-segment counters
- arp: add feature arcs: arp-reply, arp-input, arp-proxy
- avf: improved logging and added 2.5/5 Gbps speeds
- bonding: NUMA-related improvements
- crypto: add support for AES-CTR cipher
- fib
  - FIB Entry tracking
  - Support the POP of a Pseudo Wire Control Word
- gbp
  - Anonymous l3-out subnets support
  - ARP unicast forward in gbp bridge domain
  - An Endpoint can change sclass
  - Consider data-plane learnt source better than control-plane
  - VRF scoped contracts
- gso (experimental)
  - Add support to pg interfaces
  - Add support to vhost user
  - Add support to native virtio
  - Add support for tagged interfaces
- punt: allow to specify packets by IP protocol Type
- ip6-local: hop-by-hop protocol demux table
- ipsec
  - intel-ipsec-mb version 0.52
  - AH encrypt rework
  - handle UDP keepalives
  - support GCM in ESP
- virtio
  - Refactor control queue support
- dhcp-client: DSCP marking for transmitted packets
- Idle resource usage improvements
  - Allocate bihash virtual space on demand
  - gre: don't register gre input nodes unless a gre tunnel is created
  - gtpu: don't register udp ports unless a tunnel is created
  - lacp: create lacp-process on demand
  - lisp-cp: start lisp retry service on demand
  - start the cdp period and dns resolver process on demand
  - vat: unload unused vat plugins
- nat: api cleanup & update
- nsim: make available as an output feature
- load-balance performance improvements
- l2: Add support for arp unicast forwarding
- mactime
  - Mini-ACLs
  - Per-MAC allow-with-quota feature
- qos
  - QoS dump APIs
  - Store function
- rdma: add support for promiscuous mode (l2-switching and xconnect)
- sr: update the Segment Routing definition to be compliant with current in IETF
- udp-ping: disable due to conflict with mldv2
- vxlan-gpe: improve encap performance
- vom
  - QoS support
  - Bridge domain arp unicast forwarding flag
  - Bridge domain unknown unicast flooding flag

### Host stack
- session
  - API to support manual svm fifo resizing
  - Improved session output scheduler and close state machine
  - Transport and session cleanup notifications for builtin apps
  - Session migration notifications for builtin apps
  - Support for no session layer lookup transports (quic and tls)
  - Ability to retrieve local/remote endpoint in transport vft
  - Cleanup segment manager and fifo segment
  - Fix vpp to app msg generation on enqueue fail
  - Improve event logging
  - Moved test applications to hsa plugin
- tcp
  - Congestion control algorithm enhancements
  - Delivery rate estimator
  - ACK/retransmission refactor and pacing
  - Add tcp-input sibling nodes without full 6-tuple lookup
  - More RFC4898 connection statistics
  - Allow custom output next node
  - Allow custom congestion control algorithms
- quic
  - Multi-thread support
  - Logs readability improvements
  - Multistream support
- tls
  - Fix close with data and listen failures
  - Handle TCP transport rests
  - Support endpoint retrieval interface
- vcl
  - support quic streams and "connectable listeners"
  - worker unregister api
  - fix epoll with large events batch
  - ldp: add option to enable transparent TLS connections
- udp:
  - support close with data
  - fixed session migration
- sctp
  - add option to enable/disable default to disable
  - moved from vnet to plugins

