#!/bin/bash

# Copyright (c) 2015 Cisco and/or its affiliates.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at:
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

if [ -z $1 ]; then
	echo "Please specify path"
	exit 1
fi

which chrpath &> /dev/null

if [ $? -ne 0 ] ; then
	echo "Please install chrpath tool"
	exit 1
fi

cd $1

libs=$(find * -type f -name \*.so\*)
execs=$(find * -type f -path bin/\* )

echo "Setting RPATH to $2 ..."

for i in $libs $execs; do
	# in case non-ELF file is found
	chrpath $i 2> /dev/null > /dev/null
	if [ $? -eq 0 ] ; then
		echo $i
		chrpath -r $2
	fi
done

