.. _gettingstarted:

######################
Getting Started
######################

The Getting Started Guide is comprised of several different sections; a User section
that describes a basic installation and configuration of VPP (either manually or
using a config utility),  another install for Developers, which contains additional
code that provides tools that are used in a development environment.

This section covers the following:

* Describes how to manually install VPP Binaries on different OS platforms (Ubuntu, Centos) and then how to configure and use VPP.
* Describes the different types of VPP packages, which are used in both basic and developer installs.
* A VPP tutorial which is a great way to learn VPP basics.

The Users section covers configuration operations; this

* How to configure and Run VPP manually.
* How to use the Configuration Utility to install, and then configure VPP.

The Developers section covers the following areas:

* Building VPP
* Describes the components of the four VPP layers
* How to Create, Add, Enable/Disable features
* Discusses different aspects of Bounded-index Extensible Hashing (bihash)

The Writing VPP Documentation section covers the following topics:

* How to build VPP documents
* How to push your changes to the VPP Docs Repository
* Identifies the different styles associated to reStructuredText
* Identifies the different styles associated to Markdown

.. toctree::
   :maxdepth: 2

   installing/index.rst
   progressivevpp/index.rst
   users/index.rst
   developers/index.rst
   writingdocs/index.rst
