.. _vpp16.09:

###############################
Features for Release VPP 16.09
###############################

This section lists those features that became available in VPP Release 16.09:

* Integrated July 2016 DPDK release
   - DPDK-vhost is depreciated pending a complete rework of the original integration and addressing of rx performance deltas
   - Patches required for DPDK 16.07:
      + Correctly setting the Packet Type in the IGB, IXGBE and i40e drivers
      + Correctly setting checksum in the i40e driver
      + NXP DPAA2 PMD Driver
      + rte_delay (yield) functionality
* Add “in tree” plugins:
   - IPv6 ILA
   - iOAM
   - Load Balancer
   - SNAT
* High-performance (line-rate) “neutron like” L4 port-filtering
* API refactoring - addressing some of the issues around JVPP bindings
   - Accommodating plugins (e.g. NSH_SFC)
   - Binding for python
* LISP
   - L2 LISP overlays
   - Multitenancy
   - Multihoming
   - RTR mode
   - Map-resolver failover algorithm
* Support 64-bit vector lengths, huge shared-memory segments
* Dynamic IP Feature ordering
   - IP Features can now specify features they appear before and after
* 16.09 Builds
   - Ubuntu 14.04 LTS - Trusty Tahr
   - Ubuntu 16.04 LTS - Xenial Xerus
   - CentOS 7
   - More information on VPP wiki
* Performance, characterize and document performance for this release (more information on CSIT page)
   - IPv4 and IPv6 Scale - performance tests
      + Bidirectional 10k/100k/1M flows
      + 64B,570B, 1518B,9000B packet sizes
   - IPv6 iACL - performance
      + DUT1 and DUT2 are configured with IPv6 routing, two static IPv6 /64 routes and IPv6 iAcl security whitelist ingress /64 filter entries applied on links
      + TG traffic profile contains two L3 flow-groups (flow-group per direction, 253 flows per flow-group) with all packets containing Ethernet header, IPv6 header and generated payload
      + MAC addresses are matching MAC addresses of the TG node interfaces
   - L2XC VXLANoIPv4 - performance
      + DUT1 and DUT2 are configured with L2 cross-connect. VXLAN tunnels are configured between L2XCs on DUT1 and DUT2
      + TG traffic profile contains two L3 flow-groups (flow-group per direction, 253 flows per flow-group) with all packets containing Ethernet header, IPv4 header with IP protocol=61 and generated payload
      + MAC addresses are matching MAC addresses of the TG node interfaces
* Documentation
   - Autogenerated CLI documentation
   - Using doxygen to automate API/Node documentation
   - (available online)
* Resolved all static analysis issues found by Coverity
   - Beginning of 16.09 cycle: 505 issues
   - Release: 0 outstanding issues


Known issues
---------------

For the full list of issues please refer to fd.io `JIRA <https://jira.fd.io/>`_.

Issues fixed
--------------

For the full list of fixed issues please refer to:

* fd.io `JIRA <https://jira.fd.io/>`_
* git `commit log <https://git.fd.io/vpp/log/?h=stable/1609>`_