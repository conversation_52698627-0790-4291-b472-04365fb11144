.. _2016_08_10_tldkintro:

.. toctree::

##########
TLDK intro
##########

Event
-----

This presentation describes the Transport Layer Development Kit (TLDK),
which is an FD.io project that provides a set of libraries to accelerate L4-L7 protocol 
processing for DPDK-based applications. The initial implementation supports UDP,
with work on TCP in progress. The project scope also includes integration of 
TLDK libraries into Vector Packet Processing (VPP) graph nodes to provide a host stack.
This presentation was held during the DPDK US Summit on August 10th, 2016.

Speakers
--------

* <PERSON>
---------

`Presentation Powerpoint <https://wiki.fd.io/images/d/d3/Tldk%40160805u2.pptx>`_

Video
-----

`Video Presentation <https://www.youtube.com/watch?v=GiEXLrJuAxg>`_


